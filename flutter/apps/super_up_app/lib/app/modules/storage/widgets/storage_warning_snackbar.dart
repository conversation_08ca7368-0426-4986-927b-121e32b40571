// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:super_up_core/super_up_core.dart';

class StorageWarningSnackbar {
  static void show({
    required BuildContext context,
    required StorageQuota quota,
    VoidCallback? onClearStorage,
    VoidCallback? onUpgradePlan,
  }) {
    final snackBar = SnackBar(
      content: StorageWarningContent(
        quota: quota,
        onClearStorage: () {
          ScaffoldMessenger.of(context).hideCurrentSnackBar();
          onClearStorage?.call();
        },
        onUpgradePlan: () {
          ScaffoldMessenger.of(context).hideCurrentSnackBar();
          onUpgradePlan?.call();
        },
      ),
      backgroundColor: Colors.transparent,
      elevation: 0,
      behavior: SnackBarBehavior.floating,
      duration: const Duration(seconds: 8),
      margin: const EdgeInsets.all(16),
    );

    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }
}

class StorageWarningContent extends StatelessWidget {
  final StorageQuota quota;
  final VoidCallback? onClearStorage;
  final VoidCallback? onUpgradePlan;

  const StorageWarningContent({
    super.key,
    required this.quota,
    this.onClearStorage,
    this.onUpgradePlan,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CupertinoColors.systemBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: CupertinoColors.systemOrange,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Warning header
          Row(
            children: [
              Icon(
                CupertinoIcons.exclamationmark_triangle_fill,
                color: CupertinoColors.systemOrange,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Storage Almost Full',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: CupertinoColors.label,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Warning message
          Text(
            'You\'re using ${(quota.usagePercentage * 100).toStringAsFixed(1)}% of your storage space. '
            'Free up space or upgrade your plan to continue receiving media.',
            style: TextStyle(
              fontSize: 14,
              color: CupertinoColors.secondaryLabel,
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Storage progress
          Container(
            height: 6,
            decoration: BoxDecoration(
              color: CupertinoColors.systemGrey5,
              borderRadius: BorderRadius.circular(3),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: quota.usagePercentage.clamp(0.0, 1.0),
              child: Container(
                decoration: BoxDecoration(
                  color: quota.isAtLimit 
                      ? CupertinoColors.systemRed 
                      : CupertinoColors.systemOrange,
                  borderRadius: BorderRadius.circular(3),
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 4),
          
          Text(
            '${quota.readableUsed} of ${quota.readableTotal} used',
            style: TextStyle(
              fontSize: 12,
              color: CupertinoColors.tertiaryLabel,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Action buttons
          Row(
            children: [
              Expanded(
                child: CupertinoButton(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  color: CupertinoColors.systemGrey6,
                  borderRadius: BorderRadius.circular(8),
                  onPressed: onClearStorage,
                  child: Text(
                    'Clear Storage',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: CupertinoColors.label,
                    ),
                  ),
                ),
              ),
              
              const SizedBox(width: 12),
              
              Expanded(
                child: CupertinoButton(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  color: Colors.purple,
                  borderRadius: BorderRadius.circular(8),
                  onPressed: onUpgradePlan,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        CupertinoIcons.star_fill,
                        size: 16,
                        color: Colors.white,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Upgrade Plan',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class StorageFullDialog {
  static Future<void> show({
    required BuildContext context,
    required StorageQuota quota,
    VoidCallback? onClearStorage,
    VoidCallback? onUpgradePlan,
  }) async {
    return showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              CupertinoIcons.exclamationmark_triangle_fill,
              color: CupertinoColors.systemRed,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text('Storage Full'),
          ],
        ),
        content: Column(
          children: [
            const SizedBox(height: 8),
            Text(
              'Your storage is full (${quota.readableUsed} used). '
              'You need to free up space or upgrade your plan to continue using the app.',
            ),
            const SizedBox(height: 16),
            Container(
              height: 6,
              decoration: BoxDecoration(
                color: CupertinoColors.systemGrey5,
                borderRadius: BorderRadius.circular(3),
              ),
              child: Container(
                decoration: BoxDecoration(
                  color: CupertinoColors.systemRed,
                  borderRadius: BorderRadius.circular(3),
                ),
              ),
            ),
          ],
        ),
        actions: [
          CupertinoDialogAction(
            onPressed: () {
              Navigator.of(context).pop();
              onClearStorage?.call();
            },
            child: Text('Clear Storage'),
          ),
          CupertinoDialogAction(
            onPressed: () {
              Navigator.of(context).pop();
              onUpgradePlan?.call();
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  CupertinoIcons.star_fill,
                  size: 16,
                  color: Colors.purple,
                ),
                const SizedBox(width: 4),
                Text(
                  'Upgrade',
                  style: TextStyle(color: Colors.purple),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
