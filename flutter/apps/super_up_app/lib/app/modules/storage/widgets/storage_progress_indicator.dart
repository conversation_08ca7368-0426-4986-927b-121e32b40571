// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:super_up_core/super_up_core.dart';

class StorageProgressIndicator extends StatelessWidget {
  final StorageQuota quota;
  final bool showDetails;

  const StorageProgressIndicator({
    super.key,
    required this.quota,
    this.showDetails = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: CupertinoColors.systemBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: CupertinoColors.separator,
          width: 0.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Storage Usage',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: CupertinoColors.label,
                ),
              ),
              if (quota.isPremium)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.purple,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'PREMIUM',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),
          
          // Progress bar
          Container(
            height: 8,
            decoration: BoxDecoration(
              color: CupertinoColors.systemGrey5,
              borderRadius: BorderRadius.circular(4),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: quota.usagePercentage.clamp(0.0, 1.0),
              child: Container(
                decoration: BoxDecoration(
                  color: _getProgressColor(),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
          ),
          
          if (showDetails) ...[
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${quota.readableUsed} used',
                  style: TextStyle(
                    fontSize: 14,
                    color: CupertinoColors.secondaryLabel,
                  ),
                ),
                Text(
                  '${quota.readableTotal} total',
                  style: TextStyle(
                    fontSize: 14,
                    color: CupertinoColors.secondaryLabel,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              '${(quota.usagePercentage * 100).toStringAsFixed(1)}% used',
              style: TextStyle(
                fontSize: 12,
                color: quota.isNearLimit 
                    ? CupertinoColors.systemRed 
                    : CupertinoColors.tertiaryLabel,
                fontWeight: quota.isNearLimit ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getProgressColor() {
    if (quota.isAtLimit) return CupertinoColors.systemRed;
    if (quota.isNearLimit) return CupertinoColors.systemOrange;
    return CupertinoColors.systemBlue;
  }
}

class StorageTypeBreakdown extends StatelessWidget {
  final Map<MediaFileType, int> storageByType;
  final int totalStorage;

  const StorageTypeBreakdown({
    super.key,
    required this.storageByType,
    required this.totalStorage,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: CupertinoColors.systemBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: CupertinoColors.separator,
          width: 0.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Storage by Type',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: CupertinoColors.label,
            ),
          ),
          const SizedBox(height: 16),
          ...MediaFileType.values.map((type) {
            final size = storageByType[type] ?? 0;
            final percentage = totalStorage > 0 ? (size / totalStorage) : 0.0;
            return _buildTypeRow(type, size, percentage);
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildTypeRow(MediaFileType type, int size, double percentage) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: _getTypeColor(type),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getTypeIcon(type),
              color: Colors.white,
              size: 18,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getTypeName(type),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: CupertinoColors.label,
                  ),
                ),
                Text(
                  _formatSize(size),
                  style: TextStyle(
                    fontSize: 12,
                    color: CupertinoColors.secondaryLabel,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '${(percentage * 100).toStringAsFixed(1)}%',
            style: TextStyle(
              fontSize: 12,
              color: CupertinoColors.secondaryLabel,
            ),
          ),
        ],
      ),
    );
  }

  Color _getTypeColor(MediaFileType type) {
    switch (type) {
      case MediaFileType.image:
        return Colors.green;
      case MediaFileType.video:
        return Colors.blue;
      case MediaFileType.voice:
        return Colors.orange;
      case MediaFileType.file:
        return Colors.purple;
    }
  }

  IconData _getTypeIcon(MediaFileType type) {
    switch (type) {
      case MediaFileType.image:
        return CupertinoIcons.photo;
      case MediaFileType.video:
        return CupertinoIcons.videocam;
      case MediaFileType.voice:
        return CupertinoIcons.mic;
      case MediaFileType.file:
        return CupertinoIcons.doc;
    }
  }

  String _getTypeName(MediaFileType type) {
    switch (type) {
      case MediaFileType.image:
        return 'Images';
      case MediaFileType.video:
        return 'Videos';
      case MediaFileType.voice:
        return 'Voice Messages';
      case MediaFileType.file:
        return 'Files';
    }
  }

  String _formatSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)}KB';
    }
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }
}
