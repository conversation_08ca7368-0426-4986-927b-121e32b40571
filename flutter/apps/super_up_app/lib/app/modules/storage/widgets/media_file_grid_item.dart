// Copyright 2023, the hatem<PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:super_up_core/super_up_core.dart';

class MediaFileGridItem extends StatelessWidget {
  final MediaFileInfo mediaFile;
  final bool isSelected;
  final bool isSelectionMode;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  const MediaFileGridItem({
    super.key,
    required this.mediaFile,
    this.isSelected = false,
    this.isSelectionMode = false,
    this.onTap,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      onLongPress: onLongPress,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: isSelected
              ? Border.all(color: CupertinoColors.systemBlue, width: 3)
              : null,
        ),
        child: Stack(
          children: [
            // Main content
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: _buildContent(),
            ),

            // Selection overlay
            if (isSelectionMode)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: isSelected
                        ? CupertinoColors.systemBlue
                        : CupertinoColors.systemBackground.withOpacity(0.8),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected
                          ? CupertinoColors.systemBlue
                          : CupertinoColors.systemGrey,
                      width: 2,
                    ),
                  ),
                  child: isSelected
                      ? Icon(
                          CupertinoIcons.checkmark,
                          size: 14,
                          color: Colors.white,
                        )
                      : null,
                ),
              ),

            // File info overlay for non-image files
            if (mediaFile.type != MediaFileType.image)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withOpacity(0.7),
                      ],
                    ),
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(8),
                      bottomRight: Radius.circular(8),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        mediaFile.fileName,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        mediaFile.readableSize,
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 10,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent() {
    switch (mediaFile.type) {
      case MediaFileType.image:
        return _buildImageContent();
      case MediaFileType.video:
        return _buildVideoContent();
      case MediaFileType.voice:
        return _buildVoiceContent();
      case MediaFileType.file:
        return _buildFileContent();
    }
  }

  Widget _buildImageContent() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      child: File(mediaFile.filePath).existsSync()
          ? Image.file(
              File(mediaFile.filePath),
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => _buildErrorWidget(),
            )
          : _buildErrorWidget(),
    );
  }

  Widget _buildVideoContent() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: CupertinoColors.systemGrey6,
      child: Stack(
        children: [
          // Video thumbnail placeholder
          Center(
            child: Icon(
              CupertinoIcons.videocam_fill,
              size: 40,
              color: CupertinoColors.systemGrey,
            ),
          ),
          // Play button overlay
          Center(
            child: Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.6),
                shape: BoxShape.circle,
              ),
              child: Icon(
                CupertinoIcons.play_fill,
                color: Colors.white,
                size: 24,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVoiceContent() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.orange.withOpacity(0.1),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            CupertinoIcons.mic_fill,
            size: 40,
            color: Colors.orange,
          ),
          const SizedBox(height: 8),
          Text(
            'Voice',
            style: TextStyle(
              fontSize: 12,
              color: Colors.orange,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFileContent() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.purple.withOpacity(0.1),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            CupertinoIcons.doc_fill,
            size: 40,
            color: Colors.purple,
          ),
          const SizedBox(height: 8),
          Text(
            'File',
            style: TextStyle(
              fontSize: 12,
              color: Colors.purple,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: CupertinoColors.systemGrey6,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            CupertinoIcons.exclamationmark_triangle,
            size: 32,
            color: CupertinoColors.systemGrey,
          ),
          const SizedBox(height: 8),
          Text(
            'File not found',
            style: TextStyle(
              fontSize: 10,
              color: CupertinoColors.systemGrey,
            ),
          ),
        ],
      ),
    );
  }
}

class MediaFileListItem extends StatelessWidget {
  final MediaFileInfo mediaFile;
  final bool isSelected;
  final bool isSelectionMode;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  const MediaFileListItem({
    super.key,
    required this.mediaFile,
    this.isSelected = false,
    this.isSelectionMode = false,
    this.onTap,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      onLongPress: onLongPress,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected
              ? CupertinoColors.systemBlue.withOpacity(0.1)
              : CupertinoColors.systemBackground,
          border: Border(
            bottom: BorderSide(
              color: CupertinoColors.separator,
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          children: [
            // File icon/thumbnail
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: _getTypeColor().withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                _getTypeIcon(),
                color: _getTypeColor(),
                size: 24,
              ),
            ),

            const SizedBox(width: 12),

            // File info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    mediaFile.fileName,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: CupertinoColors.label,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Text(
                        mediaFile.readableSize,
                        style: TextStyle(
                          fontSize: 14,
                          color: CupertinoColors.secondaryLabel,
                        ),
                      ),
                      Text(
                        ' • ',
                        style: TextStyle(
                          fontSize: 14,
                          color: CupertinoColors.secondaryLabel,
                        ),
                      ),
                      Text(
                        _formatDate(mediaFile.createdAt),
                        style: TextStyle(
                          fontSize: 14,
                          color: CupertinoColors.secondaryLabel,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Selection indicator
            if (isSelectionMode)
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: isSelected
                      ? CupertinoColors.systemBlue
                      : CupertinoColors.systemBackground,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected
                        ? CupertinoColors.systemBlue
                        : CupertinoColors.systemGrey,
                    width: 2,
                  ),
                ),
                child: isSelected
                    ? Icon(
                        CupertinoIcons.checkmark,
                        size: 14,
                        color: Colors.white,
                      )
                    : null,
              ),
          ],
        ),
      ),
    );
  }

  Color _getTypeColor() {
    switch (mediaFile.type) {
      case MediaFileType.image:
        return Colors.green;
      case MediaFileType.video:
        return Colors.blue;
      case MediaFileType.voice:
        return Colors.orange;
      case MediaFileType.file:
        return Colors.purple;
    }
  }

  IconData _getTypeIcon() {
    switch (mediaFile.type) {
      case MediaFileType.image:
        return CupertinoIcons.photo;
      case MediaFileType.video:
        return CupertinoIcons.videocam;
      case MediaFileType.voice:
        return CupertinoIcons.mic;
      case MediaFileType.file:
        return CupertinoIcons.doc;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
