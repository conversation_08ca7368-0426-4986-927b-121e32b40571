// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:super_up_core/super_up_core.dart';
import '../views/premium_upgrade_page.dart';

enum MediaViewMode { grid, list }

class MediaManagementState {
  final List<MediaFileInfo> allFiles;
  final List<MediaFileInfo> filteredFiles;
  final Set<String> selectedFiles;
  final MediaFileType? currentFilter;
  final MediaViewMode viewMode;
  final StorageQuota? storageQuota;
  final Map<MediaFileType, int> storageByType;
  final bool isSelectionMode;

  MediaManagementState({
    this.allFiles = const [],
    this.filteredFiles = const [],
    this.selectedFiles = const {},
    this.currentFilter,
    this.viewMode = MediaViewMode.grid,
    this.storageQuota,
    this.storageByType = const {},
    this.isSelectionMode = false,
  });

  MediaManagementState copyWith({
    List<MediaFileInfo>? allFiles,
    List<MediaFileInfo>? filteredFiles,
    Set<String>? selectedFiles,
    MediaFileType? currentFilter,
    MediaViewMode? viewMode,
    StorageQuota? storageQuota,
    Map<MediaFileType, int>? storageByType,
    bool? isSelectionMode,
  }) {
    return MediaManagementState(
      allFiles: allFiles ?? this.allFiles,
      filteredFiles: filteredFiles ?? this.filteredFiles,
      selectedFiles: selectedFiles ?? this.selectedFiles,
      currentFilter: currentFilter ?? this.currentFilter,
      viewMode: viewMode ?? this.viewMode,
      storageQuota: storageQuota ?? this.storageQuota,
      storageByType: storageByType ?? this.storageByType,
      isSelectionMode: isSelectionMode ?? this.isSelectionMode,
    );
  }
}

class MediaManagementController
    extends SLoadingController<MediaManagementState> {
  final MediaStorageManagementService _storageService =
      MediaStorageManagementService();

  MediaManagementController() : super(SLoadingState(MediaManagementState()));

  @override
  void onInit() {
    loadData();
  }

  @override
  void onClose() {}

  Future<void> loadData() async {
    await vSafeApiCall<MediaManagementState>(
      onLoading: () => setStateLoading(),
      request: () async {
        // Sync files first
        await _storageService.syncMediaFiles();

        // Load all data
        final allFiles = await _storageService.getAllMediaFiles();
        final storageQuota = await _storageService.getStorageQuota();
        final storageByType = await _storageService.getStorageByType();

        return value.data.copyWith(
          allFiles: allFiles,
          filteredFiles: allFiles,
          storageQuota: storageQuota,
          storageByType: storageByType,
        );
      },
      onSuccess: (newState) {
        value.data = newState;
        setStateSuccess();

        // Check for storage warnings
        _checkStorageWarnings();
      },
      onError: (error, trace) {
        setStateError(error);
      },
    );
  }

  void filterByType(MediaFileType? type) {
    final filteredFiles = type == null
        ? value.data.allFiles
        : value.data.allFiles.where((file) => file.type == type).toList();

    value.data = value.data.copyWith(
      currentFilter: type,
      filteredFiles: filteredFiles,
      selectedFiles: {}, // Clear selection when changing filter
      isSelectionMode: false,
    );
    setStateSuccess();
  }

  void toggleViewMode() {
    final newMode = value.data.viewMode == MediaViewMode.grid
        ? MediaViewMode.list
        : MediaViewMode.grid;

    value.data = value.data.copyWith(viewMode: newMode);
    setStateSuccess();
  }

  void toggleFileSelection(String fileId) {
    final selectedFiles = Set<String>.from(value.data.selectedFiles);

    if (selectedFiles.contains(fileId)) {
      selectedFiles.remove(fileId);
    } else {
      selectedFiles.add(fileId);
    }

    value.data = value.data.copyWith(
      selectedFiles: selectedFiles,
      isSelectionMode: selectedFiles.isNotEmpty,
    );
    setStateSuccess();
  }

  void startSelectionMode(String fileId) {
    value.data = value.data.copyWith(
      selectedFiles: {fileId},
      isSelectionMode: true,
    );
    setStateSuccess();
  }

  void exitSelectionMode() {
    value.data = value.data.copyWith(
      selectedFiles: {},
      isSelectionMode: false,
    );
    setStateSuccess();
  }

  void selectAll() {
    final allFileIds = value.data.filteredFiles.map((f) => f.id).toSet();
    value.data = value.data.copyWith(
      selectedFiles: allFileIds,
      isSelectionMode: true,
    );
    setStateSuccess();
  }

  Future<void> deleteSelectedFiles(BuildContext context) async {
    if (value.data.selectedFiles.isEmpty) return;

    final confirmed = await VAppAlert.showAskYesNoDialog(
      context: context,
      title: 'Delete Files',
      content:
          'Are you sure you want to permanently delete ${value.data.selectedFiles.length} file(s)? This action cannot be undone.',
    );

    if (confirmed != 1) return;

    await vSafeApiCall<void>(
      onLoading: () => setStateLoading(),
      request: () async {
        await _storageService
            .deleteMediaFiles(value.data.selectedFiles.toList());
      },
      onSuccess: (_) {
        // Reload data after deletion
        loadData();
      },
      onError: (error, trace) {
        setStateError(error);
        VAppAlert.showOkAlertDialog(
          context: context,
          title: 'Error',
          content: 'Failed to delete files: $error',
        );
      },
    );
  }

  Future<void> clearAllStorage(BuildContext context) async {
    final confirmed = await VAppAlert.showAskYesNoDialog(
      context: context,
      title: 'Clear All Storage',
      content:
          'Are you sure you want to delete all media files? This will free up all storage space but cannot be undone.',
    );

    if (confirmed != 1) return;

    await vSafeApiCall<void>(
      onLoading: () => setStateLoading(),
      request: () async {
        await _storageService.clearAllMedia();
      },
      onSuccess: (_) {
        loadData();
        VAppAlert.showOkAlertDialog(
          context: context,
          title: 'Success',
          content: 'All media files have been deleted successfully.',
        );
      },
      onError: (error, trace) {
        setStateError(error);
        VAppAlert.showOkAlertDialog(
          context: context,
          title: 'Error',
          content: 'Failed to clear storage: $error',
        );
      },
    );
  }

  Future<void> upgradeToPremium(BuildContext context) async {
    // Navigate to premium upgrade page
    context.toPage(const PremiumUpgradePage());
  }

  void _checkStorageWarnings() {
    final quota = value.data.storageQuota;
    if (quota != null && quota.isNearLimit && !quota.isPremium) {
      // Storage warning will be shown by the UI when this state is detected
    }
  }

  // Helper getters
  int get totalSelectedSize {
    return value.data.allFiles
        .where((file) => value.data.selectedFiles.contains(file.id))
        .fold(0, (sum, file) => sum + file.fileSize);
  }

  String get selectedSizeText {
    final size = totalSelectedSize;
    if (size < 1024) return '${size}B';
    if (size < 1024 * 1024) {
      return '${(size / 1024).toStringAsFixed(1)}KB';
    }
    if (size < 1024 * 1024 * 1024) {
      return '${(size / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
    return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  List<MediaFileInfo> get currentFiles => value.data.filteredFiles;
  bool get hasSelection => value.data.selectedFiles.isNotEmpty;
  int get selectedCount => value.data.selectedFiles.length;
  bool get isGridView => value.data.viewMode == MediaViewMode.grid;
}
