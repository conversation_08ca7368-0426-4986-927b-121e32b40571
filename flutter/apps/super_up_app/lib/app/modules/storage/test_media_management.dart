// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:super_up_core/super_up_core.dart';

/// Test page to verify media management functionality
class TestMediaManagementPage extends StatefulWidget {
  const TestMediaManagementPage({super.key});

  @override
  State<TestMediaManagementPage> createState() => _TestMediaManagementPageState();
}

class _TestMediaManagementPageState extends State<TestMediaManagementPage> {
  final MediaStorageManagementService _service = MediaStorageManagementService();
  String _status = 'Ready to test';
  StorageQuota? _quota;

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: CupertinoNavigationBar(
        middle: Text('Test Media Management'),
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                'Media Management Test',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              
              SizedBox(height: 20),
              
              Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: CupertinoColors.systemGrey6,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'Status: $_status',
                  style: TextStyle(fontSize: 16),
                ),
              ),
              
              SizedBox(height: 20),
              
              if (_quota != null) ...[
                Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: CupertinoColors.systemBlue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Storage Quota',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text('Used: ${_quota!.readableUsed}'),
                      Text('Total: ${_quota!.readableTotal}'),
                      Text('Usage: ${(_quota!.usagePercentage * 100).toStringAsFixed(1)}%'),
                      Text('Premium: ${_quota!.isPremium ? "Yes" : "No"}'),
                      Text('Near Limit: ${_quota!.isNearLimit ? "Yes" : "No"}'),
                    ],
                  ),
                ),
                SizedBox(height: 20),
              ],
              
              CupertinoButton.filled(
                onPressed: _testStorageQuota,
                child: Text('Test Storage Quota'),
              ),
              
              SizedBox(height: 12),
              
              CupertinoButton.filled(
                onPressed: _testAddSampleFiles,
                child: Text('Add Sample Files'),
              ),
              
              SizedBox(height: 12),
              
              CupertinoButton.filled(
                onPressed: _testSyncFiles,
                child: Text('Sync Files'),
              ),
              
              SizedBox(height: 12),
              
              CupertinoButton.filled(
                onPressed: _testUpgradePremium,
                child: Text('Test Premium Upgrade'),
              ),
              
              SizedBox(height: 12),
              
              CupertinoButton(
                color: CupertinoColors.systemRed,
                onPressed: _testClearAll,
                child: Text('Clear All Test Data'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _testStorageQuota() async {
    setState(() {
      _status = 'Testing storage quota...';
    });

    try {
      final quota = await _service.getStorageQuota();
      setState(() {
        _quota = quota;
        _status = 'Storage quota loaded successfully';
      });
    } catch (e) {
      setState(() {
        _status = 'Error loading storage quota: $e';
      });
    }
  }

  Future<void> _testAddSampleFiles() async {
    setState(() {
      _status = 'Adding sample files...';
    });

    try {
      // Add some sample media files
      final sampleFiles = [
        MediaFileInfo(
          id: 'test_image_1',
          fileName: 'sample_image.jpg',
          filePath: '/test/path/sample_image.jpg',
          fileSize: 1024 * 1024, // 1MB
          type: MediaFileType.image,
          createdAt: DateTime.now(),
          roomId: 'test_room_1',
          messageId: 'test_msg_1',
        ),
        MediaFileInfo(
          id: 'test_video_1',
          fileName: 'sample_video.mp4',
          filePath: '/test/path/sample_video.mp4',
          fileSize: 5 * 1024 * 1024, // 5MB
          type: MediaFileType.video,
          createdAt: DateTime.now(),
          roomId: 'test_room_1',
          messageId: 'test_msg_2',
        ),
        MediaFileInfo(
          id: 'test_voice_1',
          fileName: 'sample_voice.m4a',
          filePath: '/test/path/sample_voice.m4a',
          fileSize: 512 * 1024, // 512KB
          type: MediaFileType.voice,
          createdAt: DateTime.now(),
          roomId: 'test_room_2',
          messageId: 'test_msg_3',
        ),
      ];

      for (final file in sampleFiles) {
        await _service.registerMediaFile(file);
      }

      setState(() {
        _status = 'Sample files added successfully';
      });
      
      // Refresh quota
      await _testStorageQuota();
    } catch (e) {
      setState(() {
        _status = 'Error adding sample files: $e';
      });
    }
  }

  Future<void> _testSyncFiles() async {
    setState(() {
      _status = 'Syncing files...';
    });

    try {
      await _service.syncMediaFiles();
      final files = await _service.getAllMediaFiles();
      
      setState(() {
        _status = 'Sync completed. Found ${files.length} files';
      });
      
      // Refresh quota
      await _testStorageQuota();
    } catch (e) {
      setState(() {
        _status = 'Error syncing files: $e';
      });
    }
  }

  Future<void> _testUpgradePremium() async {
    setState(() {
      _status = 'Upgrading to premium...';
    });

    try {
      await _service.upgradeToPremium();
      
      setState(() {
        _status = 'Upgraded to premium successfully';
      });
      
      // Refresh quota
      await _testStorageQuota();
    } catch (e) {
      setState(() {
        _status = 'Error upgrading to premium: $e';
      });
    }
  }

  Future<void> _testClearAll() async {
    setState(() {
      _status = 'Clearing all test data...';
    });

    try {
      await _service.clearAllMedia();
      await VAppPref.setBool('is_premium_user', false);
      
      setState(() {
        _status = 'All test data cleared';
        _quota = null;
      });
    } catch (e) {
      setState(() {
        _status = 'Error clearing test data: $e';
      });
    }
  }
}
