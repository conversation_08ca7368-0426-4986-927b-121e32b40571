// Copyright 2023, the hatem<PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:super_up_core/super_up_core.dart';

class StoragePlan {
  final String name;
  final String storage;
  final int storageBytes;
  final String price;
  final List<String> features;
  final bool isPopular;
  final Color color;

  StoragePlan({
    required this.name,
    required this.storage,
    required this.storageBytes,
    required this.price,
    required this.features,
    required this.isPopular,
    required this.color,
  });
}

class PremiumUpgradePage extends StatefulWidget {
  const PremiumUpgradePage({super.key});

  @override
  State<PremiumUpgradePage> createState() => _PremiumUpgradePageState();
}

class _PremiumUpgradePageState extends State<PremiumUpgradePage> {
  int selectedPlanIndex = 1; // Default to Premium plan

  final List<StoragePlan> plans = [
    StoragePlan(
      name: 'Basic',
      storage: '1GB',
      storageBytes: 1024 * 1024 * 1024,
      price: 'Free',
      features: [
        '1GB storage space',
        'Basic media management',
        'Standard support',
      ],
      isPopular: false,
      color: CupertinoColors.systemGrey,
    ),
    StoragePlan(
      name: 'Premium',
      storage: '10GB',
      storageBytes: 10 * 1024 * 1024 * 1024,
      price: '\$4.99/month',
      features: [
        '10GB storage space',
        'Advanced media analytics',
        'Priority support',
        'Auto-backup features',
        'No storage warnings',
      ],
      isPopular: true,
      color: Colors.purple,
    ),
    StoragePlan(
      name: 'Pro',
      storage: '50GB',
      storageBytes: 50 * 1024 * 1024 * 1024,
      price: '\$9.99/month',
      features: [
        '50GB storage space',
        'Advanced media analytics',
        'Priority support',
        'Auto-backup features',
        'Cloud sync',
        'Team collaboration',
      ],
      isPopular: false,
      color: Colors.blue,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      child: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          CupertinoSliverNavigationBar(
            largeTitle: Text('Upgrade Storage'),
          )
        ],
        body: SafeArea(
          top: false,
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16),
            child: Column(
              children: [
                // Header
                Container(
                  padding: EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.purple, Colors.blue],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        CupertinoIcons.cloud_fill,
                        size: 48,
                        color: Colors.white,
                      ),
                      SizedBox(height: 12),
                      Text(
                        'Never Run Out of Space',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Store more photos, videos, and files with our premium storage plans',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white.withOpacity(0.9),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 24),

                // Plans
                ...plans.asMap().entries.map((entry) {
                  final index = entry.key;
                  final plan = entry.value;
                  return _buildPlanCard(plan, index);
                }),

                SizedBox(height: 24),

                // Upgrade button
                Container(
                  width: double.infinity,
                  child: CupertinoButton(
                    color: plans[selectedPlanIndex].color,
                    borderRadius: BorderRadius.circular(12),
                    padding: EdgeInsets.symmetric(vertical: 16),
                    onPressed: () => _handleUpgrade(),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          CupertinoIcons.star_fill,
                          color: Colors.white,
                          size: 20,
                        ),
                        SizedBox(width: 8),
                        Text(
                          'Upgrade to ${plans[selectedPlanIndex].name}',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                SizedBox(height: 16),

                Text(
                  'Payment integration coming soon!',
                  style: TextStyle(
                    fontSize: 14,
                    color: CupertinoColors.systemGrey,
                  ),
                ),

                SizedBox(height: 8),

                Text(
                  'For now, this is a demo of the premium upgrade interface.',
                  style: TextStyle(
                    fontSize: 12,
                    color: CupertinoColors.tertiaryLabel,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlanCard(StoragePlan plan, int index) {
    final isSelected = selectedPlanIndex == index;

    return GestureDetector(
      onTap: () {
        setState(() {
          selectedPlanIndex = index;
        });
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 16),
        padding: EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: CupertinoColors.systemBackground,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? plan.color : CupertinoColors.separator,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: plan.color.withOpacity(0.2),
                    blurRadius: 8,
                    offset: Offset(0, 4),
                  ),
                ]
              : null,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: plan.color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    plan.name == 'Basic'
                        ? CupertinoIcons.folder
                        : plan.name == 'Premium'
                            ? CupertinoIcons.star_fill
                            : CupertinoIcons.sparkles,
                    color: plan.color,
                    size: 24,
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            plan.name,
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: CupertinoColors.label,
                            ),
                          ),
                          if (plan.isPopular) ...[
                            SizedBox(width: 8),
                            Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.orange,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                'POPULAR',
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      Text(
                        plan.storage,
                        style: TextStyle(
                          fontSize: 16,
                          color: plan.color,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  plan.price,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: plan.color,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            ...plan.features.map((feature) => Padding(
                  padding: EdgeInsets.only(bottom: 8),
                  child: Row(
                    children: [
                      Icon(
                        CupertinoIcons.checkmark_circle_fill,
                        color: Colors.green,
                        size: 16,
                      ),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          feature,
                          style: TextStyle(
                            fontSize: 14,
                            color: CupertinoColors.secondaryLabel,
                          ),
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }

  void _handleUpgrade() {
    final selectedPlan = plans[selectedPlanIndex];

    // Show demo dialog since payment is not implemented
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text('Demo Mode'),
        content: Text(
          'This is a demo of the premium upgrade flow. '
          'In a real app, this would integrate with payment processing '
          'to upgrade to the ${selectedPlan.name} plan (${selectedPlan.storage}) for ${selectedPlan.price}.',
        ),
        actions: [
          CupertinoDialogAction(
            child: Text('OK'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }
}
