// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:super_up_core/super_up_core.dart';
import '../controllers/media_management_controller.dart';
import '../widgets/storage_progress_indicator.dart';
import '../widgets/media_file_grid_item.dart';
import '../widgets/storage_warning_snackbar.dart';
import 'premium_upgrade_page.dart';

class MediaManagementPage extends StatefulWidget {
  const MediaManagementPage({super.key});

  @override
  State<MediaManagementPage> createState() => _MediaManagementPageState();
}

class _MediaManagementPageState extends State<MediaManagementPage>
    with TickerProviderStateMixin {
  late final MediaManagementController controller;
  late final TabController tabController;

  @override
  void initState() {
    super.initState();
    controller = MediaManagementController();
    tabController = TabController(length: 5, vsync: this);
    
    // Listen for storage warnings
    controller.addListener(_checkStorageWarnings);
  }

  @override
  void dispose() {
    controller.removeListener(_checkStorageWarnings);
    controller.dispose();
    tabController.dispose();
    super.dispose();
  }

  void _checkStorageWarnings() {
    final quota = controller.value.data.storageQuota;
    if (quota != null && quota.isNearLimit && !quota.isPremium) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        StorageWarningSnackbar.show(
          context: context,
          quota: quota,
          onClearStorage: () => controller.clearAllStorage(context),
          onUpgradePlan: () => controller.upgradeToPremium(context),
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      child: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          CupertinoSliverNavigationBar(
            largeTitle: Text('Media Management'),
            trailing: _buildTrailingActions(),
          ),
        ],
        body: ValueListenableBuilder<SLoadingState<MediaManagementState>>(
          valueListenable: controller,
          builder: (context, state, child) {
            if (state.loadingState == VChatLoadingState.loading) {
              return Center(child: CupertinoActivityIndicator());
            }

            if (state.loadingState == VChatLoadingState.error) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      CupertinoIcons.exclamationmark_triangle,
                      size: 48,
                      color: CupertinoColors.systemGrey,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Failed to load media files',
                      style: TextStyle(
                        fontSize: 16,
                        color: CupertinoColors.secondaryLabel,
                      ),
                    ),
                    SizedBox(height: 16),
                    CupertinoButton(
                      onPressed: controller.loadData,
                      child: Text('Retry'),
                    ),
                  ],
                ),
              );
            }

            return _buildContent(state.data);
          },
        ),
      ),
    );
  }

  Widget _buildTrailingActions() {
    return ValueListenableBuilder<SLoadingState<MediaManagementState>>(
      valueListenable: controller,
      builder: (context, state, child) {
        if (state.data.isSelectionMode) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: controller.exitSelectionMode,
                child: Text('Cancel'),
              ),
              CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () => controller.deleteSelectedFiles(context),
                child: Text(
                  'Delete',
                  style: TextStyle(color: CupertinoColors.systemRed),
                ),
              ),
            ],
          );
        }

        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: controller.toggleViewMode,
              child: Icon(
                controller.isGridView 
                    ? CupertinoIcons.list_bullet 
                    : CupertinoIcons.grid,
              ),
            ),
            CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: controller.selectAll,
              child: Text('Select All'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildContent(MediaManagementState data) {
    return Column(
      children: [
        // Storage overview
        if (data.storageQuota != null) ...[
          StorageProgressIndicator(quota: data.storageQuota!),
          StorageTypeBreakdown(
            storageByType: data.storageByType,
            totalStorage: data.storageQuota!.usedBytes,
          ),
        ],

        // Selection info
        if (data.isSelectionMode)
          Container(
            padding: EdgeInsets.all(16),
            color: CupertinoColors.systemBlue.withOpacity(0.1),
            child: Row(
              children: [
                Icon(
                  CupertinoIcons.checkmark_circle_fill,
                  color: CupertinoColors.systemBlue,
                ),
                SizedBox(width: 8),
                Text(
                  '${controller.selectedCount} selected (${controller.selectedSizeText})',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: CupertinoColors.systemBlue,
                  ),
                ),
              ],
            ),
          ),

        // Tabs
        Expanded(
          child: Column(
            children: [
              Container(
                color: CupertinoColors.systemBackground,
                child: TabBar(
                  controller: tabController,
                  isScrollable: true,
                  labelColor: CupertinoColors.systemBlue,
                  unselectedLabelColor: CupertinoColors.secondaryLabel,
                  indicatorColor: CupertinoColors.systemBlue,
                  onTap: (index) {
                    switch (index) {
                      case 0:
                        controller.filterByType(null);
                        break;
                      case 1:
                        controller.filterByType(MediaFileType.image);
                        break;
                      case 2:
                        controller.filterByType(MediaFileType.video);
                        break;
                      case 3:
                        controller.filterByType(MediaFileType.voice);
                        break;
                      case 4:
                        controller.filterByType(MediaFileType.file);
                        break;
                    }
                  },
                  tabs: [
                    Tab(text: 'All (${data.allFiles.length})'),
                    Tab(text: 'Images (${_getTypeCount(data.allFiles, MediaFileType.image)})'),
                    Tab(text: 'Videos (${_getTypeCount(data.allFiles, MediaFileType.video)})'),
                    Tab(text: 'Voice (${_getTypeCount(data.allFiles, MediaFileType.voice)})'),
                    Tab(text: 'Files (${_getTypeCount(data.allFiles, MediaFileType.file)})'),
                  ],
                ),
              ),

              // Content
              Expanded(
                child: data.filteredFiles.isEmpty
                    ? _buildEmptyState()
                    : _buildMediaGrid(data),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            CupertinoIcons.photo_on_rectangle,
            size: 64,
            color: CupertinoColors.systemGrey,
          ),
          SizedBox(height: 16),
          Text(
            'No media files found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: CupertinoColors.secondaryLabel,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Media files from your chats will appear here',
            style: TextStyle(
              fontSize: 14,
              color: CupertinoColors.tertiaryLabel,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMediaGrid(MediaManagementState data) {
    if (controller.isGridView) {
      return GridView.builder(
        padding: EdgeInsets.all(16),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
        ),
        itemCount: data.filteredFiles.length,
        itemBuilder: (context, index) {
          final file = data.filteredFiles[index];
          return MediaFileGridItem(
            mediaFile: file,
            isSelected: data.selectedFiles.contains(file.id),
            isSelectionMode: data.isSelectionMode,
            onTap: () => controller.toggleFileSelection(file.id),
            onLongPress: () => controller.startSelectionMode(file.id),
          );
        },
      );
    } else {
      return ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: 16),
        itemCount: data.filteredFiles.length,
        itemBuilder: (context, index) {
          final file = data.filteredFiles[index];
          return MediaFileListItem(
            mediaFile: file,
            isSelected: data.selectedFiles.contains(file.id),
            isSelectionMode: data.isSelectionMode,
            onTap: () => controller.toggleFileSelection(file.id),
            onLongPress: () => controller.startSelectionMode(file.id),
          );
        },
      );
    }
  }

  int _getTypeCount(List<MediaFileInfo> files, MediaFileType type) {
    return files.where((file) => file.type == type).length;
  }
}
