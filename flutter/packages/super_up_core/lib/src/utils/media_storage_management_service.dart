// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:convert';
import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:super_up_core/super_up_core.dart';
import 'auto_download_service.dart';

class StorageQuota {
  final int totalBytes;
  final int usedBytes;
  final bool isPremium;

  StorageQuota({
    required this.totalBytes,
    required this.usedBytes,
    this.isPremium = false,
  });

  double get usagePercentage => usedBytes / totalBytes;
  int get remainingBytes => totalBytes - usedBytes;
  bool get isNearLimit => usagePercentage >= 0.7;
  bool get isAtLimit => usagePercentage >= 1.0;

  String get readableUsed {
    if (usedBytes < 1024) return '${usedBytes}B';
    if (usedBytes < 1024 * 1024) {
      return '${(usedBytes / 1024).toStringAsFixed(1)}KB';
    }
    if (usedBytes < 1024 * 1024 * 1024) {
      return '${(usedBytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
    return '${(usedBytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  String get readableTotal {
    if (totalBytes < 1024) return '${totalBytes}B';
    if (totalBytes < 1024 * 1024) {
      return '${(totalBytes / 1024).toStringAsFixed(1)}KB';
    }
    if (totalBytes < 1024 * 1024 * 1024) {
      return '${(totalBytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
    return '${(totalBytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }
}

class MediaStorageManagementService {
  static const String _mediaFilesKey = 'media_files_registry';
  static const String _storageQuotaKey = 'storage_quota_settings';

  // Default storage quota: 1GB
  static const int _defaultQuotaBytes = 1024 * 1024 * 1024;
  // Premium storage quota: 10GB
  static const int _premiumQuotaBytes = 10 * 1024 * 1024 * 1024;

  /// Get all registered media files
  Future<List<MediaFileInfo>> getAllMediaFiles() async {
    final jsonList = VAppPref.getList(_mediaFilesKey) ?? [];
    return jsonList
        .map((json) => MediaFileInfo.fromJson(jsonDecode(json)))
        .toList();
  }

  /// Get media files filtered by type
  Future<List<MediaFileInfo>> getMediaFilesByType(MediaFileType type) async {
    final allFiles = await getAllMediaFiles();
    return allFiles.where((file) => file.type == type).toList();
  }

  /// Register a new media file
  Future<void> registerMediaFile(MediaFileInfo mediaFile) async {
    final allFiles = await getAllMediaFiles();

    // Check if file already exists
    final existingIndex =
        allFiles.indexWhere((file) => file.id == mediaFile.id);
    if (existingIndex != -1) {
      allFiles[existingIndex] = mediaFile;
    } else {
      allFiles.add(mediaFile);
    }

    await _saveMediaFiles(allFiles);
  }

  /// Remove media file from registry
  Future<void> unregisterMediaFile(String fileId) async {
    final allFiles = await getAllMediaFiles();
    allFiles.removeWhere((file) => file.id == fileId);
    await _saveMediaFiles(allFiles);
  }

  /// Delete media files and update registry
  Future<void> deleteMediaFiles(List<String> fileIds) async {
    final allFiles = await getAllMediaFiles();
    final filesToDelete =
        allFiles.where((file) => fileIds.contains(file.id)).toList();

    // Delete physical files
    for (final fileInfo in filesToDelete) {
      final file = File(fileInfo.filePath);
      if (await file.exists()) {
        await file.delete();
      }
    }

    // Remove from registry
    allFiles.removeWhere((file) => fileIds.contains(file.id));
    await _saveMediaFiles(allFiles);
  }

  /// Get current storage quota information
  Future<StorageQuota> getStorageQuota() async {
    final isPremium = await _isPremiumUser();
    final totalBytes = isPremium ? _premiumQuotaBytes : _defaultQuotaBytes;
    final usedBytes = await _calculateUsedStorage();

    return StorageQuota(
      totalBytes: totalBytes,
      usedBytes: usedBytes,
      isPremium: isPremium,
    );
  }

  /// Calculate total used storage from registered files
  Future<int> _calculateUsedStorage() async {
    final allFiles = await getAllMediaFiles();
    return allFiles.fold<int>(0, (sum, file) => sum + file.fileSize);
  }

  /// Check if user has premium subscription
  Future<bool> _isPremiumUser() async {
    return VAppPref.getBool('is_premium_user');
  }

  /// Save media files to preferences
  Future<void> _saveMediaFiles(List<MediaFileInfo> files) async {
    final jsonList = files.map((file) => jsonEncode(file.toJson())).toList();
    await VAppPref.setList(_mediaFilesKey, jsonList);
  }

  /// Scan and sync media files from file system
  Future<void> syncMediaFiles() async {
    final rootPath = VFileUtils.downloadPath();
    final directory = Directory(rootPath);

    if (!await directory.exists()) {
      return;
    }

    final allFiles = await getAllMediaFiles();
    final existingPaths = allFiles.map((f) => f.filePath).toSet();

    await for (final entity in directory.list(recursive: true)) {
      if (entity is File && !existingPaths.contains(entity.path)) {
        final fileInfo = await _createMediaFileInfoFromFile(entity);
        if (fileInfo != null) {
          await registerMediaFile(fileInfo);
        }
      }
    }
  }

  /// Create MediaFileInfo from a file
  Future<MediaFileInfo?> _createMediaFileInfoFromFile(File file) async {
    try {
      final stat = await file.stat();
      final fileName = path.basename(file.path);
      final extension = path.extension(fileName).toLowerCase();

      MediaFileType? type;
      if (['.jpg', '.jpeg', '.png', '.gif', '.webp'].contains(extension)) {
        type = MediaFileType.image;
      } else if (['.mp4', '.mov', '.avi', '.mkv'].contains(extension)) {
        type = MediaFileType.video;
      } else if (['.mp3', '.wav', '.aac', '.m4a'].contains(extension)) {
        type = MediaFileType.voice;
      } else if (['.pdf', '.doc', '.docx', '.txt'].contains(extension)) {
        type = MediaFileType.file;
      }

      if (type == null) return null;

      return MediaFileInfo(
        id: fileName,
        fileName: fileName,
        filePath: file.path,
        fileSize: stat.size,
        type: type,
        createdAt: stat.modified,
      );
    } catch (e) {
      return null;
    }
  }

  /// Get storage usage by file type
  Future<Map<MediaFileType, int>> getStorageByType() async {
    final allFiles = await getAllMediaFiles();
    final usage = <MediaFileType, int>{};

    for (final type in MediaFileType.values) {
      usage[type] = allFiles
          .where((file) => file.type == type)
          .fold(0, (sum, file) => sum + file.fileSize);
    }

    return usage;
  }

  /// Clear all media files
  Future<void> clearAllMedia() async {
    final allFiles = await getAllMediaFiles();
    final fileIds = allFiles.map((f) => f.id).toList();
    await deleteMediaFiles(fileIds);
  }

  /// Upgrade to premium
  Future<void> upgradeToPremium() async {
    await VAppPref.setBool('is_premium_user', true);
  }
}
