import 'package:super_up_core/super_up_core.dart';

enum MediaDownloadOptions { images, videos, files }

enum MediaFileType { image, video, voice, file }

class MediaFileInfo {
  final String id;
  final String fileName;
  final String filePath;
  final int fileSize;
  final MediaFileType type;
  final DateTime createdAt;
  final String? roomId;
  final String? messageId;

  MediaFileInfo({
    required this.id,
    required this.fileName,
    required this.filePath,
    required this.fileSize,
    required this.type,
    required this.createdAt,
    this.roomId,
    this.messageId,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fileName': fileName,
      'filePath': filePath,
      'fileSize': fileSize,
      'type': type.name,
      'createdAt': createdAt.toIso8601String(),
      'roomId': roomId,
      'messageId': messageId,
    };
  }

  factory MediaFileInfo.fromJson(Map<String, dynamic> json) {
    return MediaFileInfo(
      id: json['id'],
      fileName: json['fileName'],
      filePath: json['filePath'],
      fileSize: json['fileSize'],
      type: MediaFileType.values.byName(json['type']),
      createdAt: DateTime.parse(json['createdAt']),
      roomId: json['roomId'],
      messageId: json['messageId'],
    );
  }

  String get readableSize {
    if (fileSize < 1024) return '${fileSize}B';
    if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    }
    if (fileSize < 1024 * 1024 * 1024) {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
    return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }
}

class AutoDownloadMediaService {
  Future<void> updateMediaDownloadOptionsForData({
    required List<MediaDownloadOptions> options,
  }) async {
    await VAppPref.setList(
      SStorageKeys.mobileDataMediaDownloadOptions.name,
      options.map((e) => "mobile-${e.name}").toList(),
    );
  }

  List<MediaDownloadOptions> getMediaDownloadOptionsForData() {
    final list = VAppPref.getList(
      SStorageKeys.mobileDataMediaDownloadOptions.name,
    );
    if (list == null) return [MediaDownloadOptions.images];
    return list
        .map((e) => MediaDownloadOptions.values.byName(e.split("mobile-").last))
        .toList();
  }

  bool get mobileImage =>
      getMediaDownloadOptionsForData().contains(MediaDownloadOptions.images);

  bool get wifiImage =>
      getMediaDownloadOptionsForWifi().contains(MediaDownloadOptions.images);

  bool get mobileFile =>
      getMediaDownloadOptionsForData().contains(MediaDownloadOptions.files);

  bool get wifiFile =>
      getMediaDownloadOptionsForWifi().contains(MediaDownloadOptions.files);

  bool get mobileVideo =>
      getMediaDownloadOptionsForData().contains(MediaDownloadOptions.videos);

  bool get wifiVideo =>
      getMediaDownloadOptionsForWifi().contains(MediaDownloadOptions.videos);

  Future<void> updateMediaDownloadOptionsForWifi({
    required List<MediaDownloadOptions> options,
  }) async {
    await VAppPref.setList(
      SStorageKeys.wifiMediaDownloadOptions.name,
      options.map((e) => "wifi-${e.name}").toList(),
    );
  }

  List<MediaDownloadOptions> getMediaDownloadOptionsForWifi() {
    final list = VAppPref.getList(
      SStorageKeys.wifiMediaDownloadOptions.name,
    );
    if (list == null) return MediaDownloadOptions.values;

    return list
        .map((e) => MediaDownloadOptions.values.byName(e.split("wifi-").last))
        .toList();
  }
}
